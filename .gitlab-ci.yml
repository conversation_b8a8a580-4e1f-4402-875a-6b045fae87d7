# yaml-language-server: $schema=https://gitlab.com/gitlab-org/gitlab/-/raw/master/app/assets/javascripts/editor/schema/ci.json

# Déclenche sur :
# 1) tout push sur develop
# 2) toute MR visant develop (depuis n'importe quelle branche)
workflow:
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
      when: always
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"'
      when: always
    - when: never

variables:
  MAVEN_IMAGE: "maven:3.9.8-eclipse-temurin-8-alpine"
  MAVEN_OPTS: "-Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository"
  MODULE: "core-gateway"
  JAR_GLOB: "$MODULE/target/*.jar"
  APP_NAME: "ms-core-gateway"
  APP_PORT: "8090"
  APP_USER: "admin"
  DEPLOY_BASE: "/opt/apps"
  JAVA_PACKAGE: "openjdk-8-jre-headless"
  SERVICE_NAME: "ms-core-gateway.service"
  APP_DIR: "/opt/core-gateway"
  JAVA_BIN: "/usr/bin/java"

stages:
  - build
  - test

cache:
  key: "maven-cache-$CI_PROJECT_NAME"
  paths:
    - .m2/repository

# 1) Build — produit le JAR du module core-gateway
build:
  stage: build
  image: $MAVEN_IMAGE
  script:
    - echo ">> Sanity check"
    - test -f "$MODULE/pom.xml" || { echo "❌ $MODULE/pom.xml introuvable"; ls -la; exit 1; }
    - mvn -v
    - echo ">> Build $MODULE (+ deps)"
    - |
      set -e
      mvn -B -DskipTests=false -pl "$MODULE" -am clean package | tee build.log
      status=${PIPESTATUS[0]}
      if [ $status -ne 0 ]; then
        echo "❌ Maven a échoué. Extraits utiles :"
        (grep -nE "^\[ERROR\]|\[FATAL\]" -n build.log | tail -n 50) || true
        exit $status
      fi
    - echo ">> Collecte artefacts"
    - mkdir -p artifacts
    - |
      # jar/war (évite d’échouer sur glob vide)
      found=0
      while IFS= read -r f; do
        echo "→ $f"; cp -v "$f" artifacts/; found=1
      done < <(find "$MODULE/target" -maxdepth 1 -type f \( -name "*.jar" -o -name "*.war" \) \
               ! -name "*sources*.jar" ! -name "*javadoc*.jar")
      if [ $found -eq 0 ]; then
        echo "❌ Aucun artefact produit dans $MODULE/target"
        echo "Dernières lignes du log Maven :"
        tail -n 200 build.log || true
        exit 1
      fi
    - echo "✅ Artefacts collectés :"; ls -l artifacts
  artifacts:
    when: always
    expire_in: 7 days
    paths:
      - artifacts/*
  rules:
    # adapte selon ton besoin
    - if: '$CI_COMMIT_BRANCH == "develop"'
      when: always
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"'
      when: always
    - when: never
  tags: [cicd-1.1]

# 2) Tests unitaires
test:
  stage: test
  image: $MAVEN_IMAGE
  needs: ["build"]
  script:
    - mvn -B -pl "$MODULE" test
  retry: 2
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
      when: always
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"'
      when: always
    - when: never
  tags: [cicd-1.1]
